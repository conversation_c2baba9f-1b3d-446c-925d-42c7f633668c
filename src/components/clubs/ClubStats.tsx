interface ClubStatsProps {
  club: {
    id: number
    name: string
    memberCount?: number
    feedItemsCount?: number
  }
  content: any
}

export function ClubStats({ club, content }: ClubStatsProps) {
  const stats = [
    {
      label: 'Fans on Platform',
      value: club.memberCount?.toLocaleString() || '0',
      icon: '👥'
    },
    {
      label: 'League Titles',
      value: content?.achievements?.[0]?.split(' ')[0] || '0',
      icon: '🏆'
    },
    {
      label: 'Founded',
      value: content?.founded || 'N/A',
      icon: '📅'
    },
    {
      label: 'Stadium',
      value: content?.stadium || 'N/A',
      icon: '🏟️'
    }
  ]

  return (
    <div className="bg-white dark:bg-neutral-800 py-8">
      <div className="container-mobile">
        <div className="grid grid-cols-2 gap-6 md:grid-cols-4">
          {stats.map((stat, index) => (
            <div key={index} className="text-center">
              <div className="mb-2 text-2xl">{stat.icon}</div>
              <div className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 md:text-3xl">
                {stat.value}
              </div>
              <div className="text-sm text-neutral-600 dark:text-neutral-400">
                {stat.label}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
